import pandas as pd
import numpy as np
from sklearn import metrics
import sys

# 添加当前目录到路径并导入NSG
sys.path.append('.')
from adsal import NSG

def rigidWt(x, sCutoff=0.85):
    y = np.ones(shape=x.shape)
    y[x < sCutoff] = 0
    return y

def expWt(x, a=10, eps=1e-6):
    # a = 3, <PERSON> et al. JCIM, 2018
    return np.exp(-a*(1-x)/(x + eps))

# 权重函数参数设置
wtFunc1a = rigidWt
kw1a = {'sCutoff':0.80}
wtFunc2a = rigidWt
kw2a = {'sCutoff':0.80}
wtFunc1b = expWt
kw1b = {'a':10}
wtFunc2b = expWt
kw2b = {'a':10}

# 加载回归任务数据
dfTr = pd.read_excel('TrainingSet_Regression.xlsx', index_col='index')
dfEx = pd.read_excel('ExternalSet_pred_Regression.xlsx', index_col='index')

# NSG网络构建
nsg = NSG(dfTr, yCol='y', smiCol='smiles')
# 可选择的指纹类型: 'MACCS_keys', 'Morgan(bit)', 'Avalon', 'PubChem'，'top_torsion‘， 
# 'atom_pair(bit)', 'atom_pair(count)', 'topology', 'Morgan(count)' 等
nsg.calcPairwiseSimilarityWithFp('MACCS_keys')  # MACCS指纹(固定167位)
dfQTSM = nsg.genQTSM(dfEx, 'smiles')

# 计算应用域指标
dfEx = dfEx.join(nsg.queryADMetrics(dfQTSM
                                    , wtFunc1=wtFunc1a, kw1=kw1a
                                    , wtFunc2=wtFunc2a, kw2=kw2a
                                    , code='|rigid'))
dfEx = dfEx.join(nsg.queryADMetrics(dfQTSM
                                    , wtFunc1=wtFunc1b, kw1=kw1b
                                    , wtFunc2=wtFunc2b, kw2=kw2b
                                    , code='|exp'))

dfEx.to_csv('dfEx_ADMetrics_Regression.csv')

# 读取数据进行分析
dfPlot = pd.read_csv('dfEx_ADMetrics_Regression.csv')
densDict = {
    'rigid': [1, 2, 3, 5, 12, 20],
    'exp': [1e-10, 0.0001, 0.01, 0.02, 0.03, 0.04, 0.1, 0.2, 1]
}

yt = dfPlot['y_true']
yp = dfPlot['y_pred']  # 回归任务使用预测值
IAValList = [0.25, 0.3, 0.4, 0.5, 0.6, 0.7, 0.8, 0.9]

for code in ['rigid', 'exp']:
    dfn = pd.DataFrame(index=IAValList, columns=densDict[code])
    dfR2 = pd.DataFrame(index=IAValList, columns=densDict[code])     # R²决定系数
    dfMAE = pd.DataFrame(index=IAValList, columns=densDict[code])    # 平均绝对误差
    dfMSE = pd.DataFrame(index=IAValList, columns=densDict[code])    # 均方误差
    dfRMSE = pd.DataFrame(index=IAValList, columns=densDict[code])   # 均方根误差
    dfMAPE = pd.DataFrame(index=IAValList, columns=densDict[code])   # 平均绝对百分比误差
    
    for densLB in densDict[code]:
        for LdUB in IAValList:
            adi = dfPlot.index[(dfPlot['simiDensity|'+code] >= densLB) & 
                              (dfPlot['simiWtLD_w|'+code] <= LdUB)]
            dfn.loc[LdUB, densLB] = adi.shape[0]
            
            if adi.shape[0] > 1:
                try:
                    yt_subset = yt[adi]
                    yp_subset = yp[adi]
                    
                    dfR2.loc[LdUB, densLB] = metrics.r2_score(yt_subset, yp_subset)
                    dfMAE.loc[LdUB, densLB] = metrics.mean_absolute_error(yt_subset, yp_subset)
                    dfMSE.loc[LdUB, densLB] = metrics.mean_squared_error(yt_subset, yp_subset)
                    dfRMSE.loc[LdUB, densLB] = np.sqrt(metrics.mean_squared_error(yt_subset, yp_subset))
                    
                    # 计算MAPE，避免除零错误
                    denominator = np.where(np.abs(yt_subset) > 1e-8, yt_subset, 1e-8)
                    mape = np.mean(np.abs((yt_subset - yp_subset) / denominator)) * 100
                    dfMAPE.loc[LdUB, densLB] = mape
                    
                except:
                    dfR2.loc[LdUB, densLB] = np.nan
                    dfMAE.loc[LdUB, densLB] = np.nan
                    dfMSE.loc[LdUB, densLB] = np.nan
                    dfRMSE.loc[LdUB, densLB] = np.nan
                    dfMAPE.loc[LdUB, densLB] = np.nan
            else:
                dfR2.loc[LdUB, densLB] = np.nan
                dfMAE.loc[LdUB, densLB] = np.nan
                dfMSE.loc[LdUB, densLB] = np.nan
                dfRMSE.loc[LdUB, densLB] = np.nan
                dfMAPE.loc[LdUB, densLB] = np.nan
    
    # 保存结果
    dfn.to_csv('model_{:s}_AD_n_regression.csv'.format(code))
    dfR2.to_csv('model_{:s}_AD_R2_regression.csv'.format(code))
    dfMAE.to_csv('model_{:s}_AD_MAE_regression.csv'.format(code))
    dfMSE.to_csv('model_{:s}_AD_MSE_regression.csv'.format(code))
    dfRMSE.to_csv('model_{:s}_AD_RMSE_regression.csv'.format(code))
    dfMAPE.to_csv('model_{:s}_AD_MAPE_regression.csv'.format(code))
