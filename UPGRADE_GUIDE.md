# ADSAL Package Upgrade Guide

## 升级概述
本指南描述了如何将adsal包从Python 3.7环境升级到Python 3.9和RDKit 2025.3.5环境。

## 主要变更

### 1. 兼容性修复
- **scipy.interp 弃用修复**: 将`from scipy import interp`更改为`from scipy import interpolate`
- **插值函数更新**: 将`interp()`调用更新为`interpolate.interp1d()`的现代语法
- **变量名修复**: 修复了多个函数中的变量名不一致问题
- **graphviz可选依赖**: 添加了graphviz导入的错误处理，当graphviz不可用时使用spring layout作为替代

### 2. 依赖包版本要求
```
Python >= 3.9
rdkit >= 2025.3.0
numpy >= 1.21.0
pandas >= 1.3.0
scipy >= 1.7.0
scikit-learn >= 1.0.0
matplotlib >= 3.5.0
networkx >= 2.6.0
pyvis >= 0.1.9
```

### 3. 具体修改内容

#### metAppDomain_ADM.py
1. **第15行**: `from scipy import interp` → `from scipy import interpolate`
2. **第685行**: 更新插值函数调用为现代语法
3. **第878-883行**: 添加graphviz导入的错误处理
4. **第1155-1162行**: 修复变量名错误 (`sortedIdx` → `self.sortedIdx`, `cliqueL` → `self.cliqueL`)
5. **第854-861行**: 修复函数参数名错误 (`yValsA` → `yValsList`)
6. **第289-294行**: 修复参数名不一致 (`PC_Space` → `PC_space`)

## 升级步骤

### 方法1: 创建新环境（推荐）
```bash
# 创建新的conda环境
conda create -n AD_upgraded python=3.9 -y

# 激活环境
conda activate AD_upgraded

# 安装RDKit 2025
conda install -c conda-forge rdkit=2025.3.5 -y

# 安装其他依赖
pip install -r requirements.txt
```

### 方法2: 升级现有环境
```bash
# 激活现有AD环境
conda activate AD

# 升级Python（如果可能）
conda install python=3.9 -y

# 安装RDKit
conda install -c conda-forge rdkit=2025.3.5 -y

# 升级其他包
pip install --upgrade numpy pandas scipy scikit-learn matplotlib networkx
```

## 验证升级

### 1. 测试导入
```python
# 测试基本导入
from adsal import NSG
import rdkit
print(f"RDKit version: {rdkit.__version__}")

# 测试核心功能
import pandas as pd
df = pd.DataFrame({'smiles': ['CCO', 'CCC'], 'y': [1, 2]})
nsg = NSG(df, yCol='y', smiCol='smiles')
print("NSG initialization successful")
```

### 2. 运行测试脚本
```python
# 运行AD_Reg.py脚本（需要相应的数据文件）
python AD_Reg.py
```

## 注意事项

1. **数据文件**: 确保`TrainingSet_Regression.xlsx`和`ExternalSet_pred_Regression.xlsx`文件存在
2. **可选依赖**: graphviz是可选的，如果不安装也不会影响核心功能
3. **内存使用**: 新版本的依赖包可能有不同的内存使用模式
4. **性能**: RDKit 2025可能在某些操作上有性能改进

## 故障排除

### 常见问题
1. **ImportError**: 确保所有依赖包都已正确安装
2. **版本冲突**: 使用`pip list`检查包版本
3. **内存错误**: 对于大数据集，可能需要调整批处理大小

### 回滚方案
如果升级后出现问题，可以：
1. 保留原始AD环境作为备份
2. 使用`conda env export`导出环境配置
3. 必要时可以回滚到原始环境

## 性能优化建议

1. **使用conda-forge**: RDKit从conda-forge安装通常更稳定
2. **批处理**: 对于大数据集，考虑分批处理
3. **并行计算**: 新版本scikit-learn支持更好的并行计算

## 联系支持
如果在升级过程中遇到问题，请检查：
1. Python和包版本兼容性
2. 数据文件格式
3. 系统资源（内存、磁盘空间）
