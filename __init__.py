"""
ADSAL - Applicability Domain Structure-Activity Landscape
A Python package for molecular applicability domain analysis and structure-activity landscape modeling.

Upgraded for Python 3.9+ and RDKit 2025+ compatibility.
"""

import sys
import warnings

# Version compatibility check
def check_python_version():
    """Check if Python version is compatible"""
    if sys.version_info < (3, 7):
        raise RuntimeError("ADSAL requires Python 3.7 or higher")
    elif sys.version_info < (3, 9):
        warnings.warn("ADSAL is optimized for Python 3.9+. Current version may have compatibility issues.",
                     UserWarning)

def check_dependencies():
    """Check if required dependencies are available"""
    missing_deps = []

    try:
        import numpy
        if tuple(map(int, numpy.__version__.split('.')[:2])) < (1, 21):
            warnings.warn(f"NumPy {numpy.__version__} detected. Recommended: 1.21+", UserWarning)
    except ImportError:
        missing_deps.append("numpy>=1.21.0")

    try:
        import pandas
        if tuple(map(int, pandas.__version__.split('.')[:2])) < (1, 3):
            warnings.warn(f"Pandas {pandas.__version__} detected. Recommended: 1.3+", UserWarning)
    except ImportError:
        missing_deps.append("pandas>=1.3.0")

    try:
        import rdkit
        # RDKit version format might be different, so just check if it exists
    except ImportError:
        missing_deps.append("rdkit>=2025.3.0")

    try:
        import sklearn
    except ImportError:
        missing_deps.append("scikit-learn>=1.0.0")

    if missing_deps:
        raise ImportError(f"Missing required dependencies: {', '.join(missing_deps)}")

# Perform compatibility checks
check_python_version()
check_dependencies()

# Import main classes
from .metAppDomain_ADM import SrKDE, HullShade, AppDomainX, AppDomainFpSimilarity, ROCMaster, NSG, NSGVisualizer

__all__ = ['SrKDE',
         'HullShade',
         'AppDomainX',
         'AppDomainFpSimilarity',
         'ROCMaster',
         'NSG',
         'NSGVisualizer']

__version__ = "0.6.5"  # Updated version for Python 3.9+ compatibility