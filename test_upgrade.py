#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
测试脚本：验证adsal包升级后的功能
用于验证Python 3.9和RDKit 2025.3.5环境下的兼容性
"""

import sys
import traceback
import pandas as pd
import numpy as np

def test_basic_imports():
    """测试基本导入功能"""
    print("=" * 50)
    print("测试1: 基本导入功能")
    print("=" * 50)
    
    try:
        # 测试Python版本
        print(f"Python版本: {sys.version}")
        
        # 测试核心科学计算包
        import numpy as np
        import pandas as pd
        import scipy
        from scipy import interpolate  # 测试新的interpolate导入
        print(f"NumPy版本: {np.__version__}")
        print(f"Pandas版本: {pd.__version__}")
        print(f"SciPy版本: {scipy.__version__}")
        
        # 测试机器学习包
        import sklearn
        print(f"Scikit-learn版本: {sklearn.__version__}")
        
        # 测试RDKit
        import rdkit
        from rdkit import Chem
        from rdkit.Chem import AllChem
        print(f"RDKit版本: {rdkit.__version__}")
        
        # 测试可视化包
        import matplotlib
        import networkx as nx
        print(f"Matplotlib版本: {matplotlib.__version__}")
        print(f"NetworkX版本: {nx.__version__}")
        
        # 测试pyvis（可选）
        try:
            import pyvis
            print(f"Pyvis版本: {pyvis.__version__}")
        except ImportError:
            print("Pyvis未安装（可选依赖）")
        
        print("✓ 所有基本导入测试通过")
        return True
        
    except Exception as e:
        print(f"✗ 基本导入测试失败: {e}")
        traceback.print_exc()
        return False

def test_adsal_import():
    """测试adsal包导入"""
    print("\n" + "=" * 50)
    print("测试2: adsal包导入")
    print("=" * 50)
    
    try:
        # 添加当前目录到路径
        sys.path.append('.')
        
        # 测试adsal导入
        from adsal import NSG, AppDomainX, AppDomainFpSimilarity
        print("✓ adsal核心类导入成功")
        
        # 测试版本信息
        import adsal
        if hasattr(adsal, '__version__'):
            print(f"adsal版本: {adsal.__version__}")
        
        return True
        
    except Exception as e:
        print(f"✗ adsal导入测试失败: {e}")
        traceback.print_exc()
        return False

def test_rdkit_functionality():
    """测试RDKit基本功能"""
    print("\n" + "=" * 50)
    print("测试3: RDKit功能测试")
    print("=" * 50)
    
    try:
        from rdkit import Chem
        from rdkit.Chem import AllChem, MACCSkeys
        from rdkit import DataStructs
        
        # 测试分子创建
        smiles = ['CCO', 'CCC', 'c1ccccc1']
        mols = [Chem.MolFromSmiles(smi) for smi in smiles]
        print(f"✓ 成功创建{len(mols)}个分子对象")
        
        # 测试指纹计算
        fps_morgan = [AllChem.GetMorganFingerprintAsBitVect(mol, 2, nBits=1024) for mol in mols]
        fps_maccs = [MACCSkeys.GenMACCSKeys(mol) for mol in mols]
        print("✓ Morgan和MACCS指纹计算成功")
        
        # 测试相似性计算
        similarity = DataStructs.TanimotoSimilarity(fps_morgan[0], fps_morgan[1])
        print(f"✓ Tanimoto相似性计算成功: {similarity:.3f}")
        
        return True
        
    except Exception as e:
        print(f"✗ RDKit功能测试失败: {e}")
        traceback.print_exc()
        return False

def test_nsg_basic_functionality():
    """测试NSG基本功能"""
    print("\n" + "=" * 50)
    print("测试4: NSG基本功能测试")
    print("=" * 50)
    
    try:
        from adsal import NSG
        
        # 创建测试数据
        test_data = pd.DataFrame({
            'smiles': ['CCO', 'CCC', 'CCCO', 'CCCC', 'c1ccccc1'],
            'y': [1.2, 1.5, 1.8, 2.1, 3.0],
            'index': range(5)
        })
        test_data.set_index('index', inplace=True)
        
        # 创建NSG对象
        nsg = NSG(test_data, yCol='y', smiCol='smiles')
        print("✓ NSG对象创建成功")
        
        # 测试指纹相似性计算
        nsg.calcPairwiseSimilarityWithFp('MACCS_keys')
        print("✓ MACCS指纹相似性计算成功")
        
        # 创建查询数据
        query_data = pd.DataFrame({
            'smiles': ['CCCCO', 'c1ccccc1O'],
            'index': [10, 11]
        })
        query_data.set_index('index', inplace=True)
        
        # 测试查询相似性矩阵生成
        dfQTSM = nsg.genQTSM(query_data, 'smiles')
        print(f"✓ 查询相似性矩阵生成成功，形状: {dfQTSM.shape}")
        
        return True
        
    except Exception as e:
        print(f"✗ NSG功能测试失败: {e}")
        traceback.print_exc()
        return False

def test_scipy_interpolate():
    """测试scipy插值功能（修复后的功能）"""
    print("\n" + "=" * 50)
    print("测试5: SciPy插值功能测试")
    print("=" * 50)
    
    try:
        from scipy import interpolate
        import numpy as np
        
        # 创建测试数据
        x = np.array([0, 1, 2, 3, 4])
        y = np.array([0, 1, 4, 9, 16])
        
        # 测试插值
        f = interpolate.interp1d(x, y, kind='linear')
        x_new = np.array([0.5, 1.5, 2.5])
        y_new = f(x_new)
        
        print(f"✓ 插值计算成功: {y_new}")
        
        # 测试边界处理
        f_bounded = interpolate.interp1d(x, y, kind='linear', bounds_error=False, fill_value=0)
        y_bounded = f_bounded(np.array([-1, 5]))
        print(f"✓ 边界处理测试成功: {y_bounded}")
        
        return True
        
    except Exception as e:
        print(f"✗ SciPy插值测试失败: {e}")
        traceback.print_exc()
        return False

def main():
    """主测试函数"""
    print("ADSAL包升级兼容性测试")
    print("=" * 60)
    
    tests = [
        test_basic_imports,
        test_adsal_import,
        test_rdkit_functionality,
        test_nsg_basic_functionality,
        test_scipy_interpolate
    ]
    
    results = []
    for test in tests:
        try:
            result = test()
            results.append(result)
        except Exception as e:
            print(f"测试执行异常: {e}")
            results.append(False)
    
    # 总结
    print("\n" + "=" * 60)
    print("测试结果总结")
    print("=" * 60)
    
    passed = sum(results)
    total = len(results)
    
    print(f"通过测试: {passed}/{total}")
    
    if passed == total:
        print("🎉 所有测试通过！升级成功！")
        return 0
    else:
        print("⚠️  部分测试失败，请检查环境配置")
        return 1

if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
