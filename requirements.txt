# Requirements for adsal package - Python 3.9 and RDKit 2025 compatible
# Core scientific computing
numpy>=1.21.0
pandas>=1.3.0
scipy>=1.7.0

# Machine learning
scikit-learn>=1.0.0

# Chemistry
rdkit>=2025.3.0

# Visualization
matplotlib>=3.5.0
pyvis>=0.1.9

# Network analysis
networkx>=2.6.0

# Optional dependencies for enhanced functionality
# graphviz  # Optional: for better network layouts
# jupyter   # Optional: for notebook support
